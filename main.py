import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from ttkthemes import ThemedTk
from PIL import Image, ImageTk
import json
import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import seaborn as sns
from datetime import datetime, timedelta
import calendar
from tkinter import colorchooser
import arabic_reshaper
from bidi.algorithm import get_display
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import numpy as np
import time
import shutil
import zipfile
import logging
import hashlib
import sqlite3
from pathlib import Path

# Setup logging
def setup_logging():
    """Setup comprehensive logging system"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'system.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    # Create specific loggers
    loggers = {
        'system': logging.getLogger('system'),
        'user': logging.getLogger('user'),
        'data': logging.getLogger('data'),
        'backup': logging.getLogger('backup'),
        'error': logging.getLogger('error')
    }

    return loggers

# Initialize logging
LOGGERS = setup_logging()

# Enhanced security functions
def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verify password against hash"""
    return hash_password(password) == hashed

# Register Arabic font
try:
    pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
    LOGGERS['system'].info("Arabic font registered successfully")
except Exception as e:
    LOGGERS['error'].error(f"Failed to register Arabic font: {e}")
    # Don't show messagebox here as GUI isn't initialized yet

class EducationalManagementSystem:
    def __init__(self):
        try:
            LOGGERS['system'].info("Initializing Educational Management System")

            self.root = ThemedTk(theme="arc")
            self.root.title(self.reshaper_text("نظام إدارة المؤسسة التعليمية"))
            self.root.geometry("1200x800")
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # Initialize enhanced user management
            self.init_user_system()
            self.current_user_role = None
            self.current_username = None
            self.session_start_time = None

            self.students_data = pd.DataFrame(columns=[
                "الاسم", "اللقب", "تاريخ الميلاد", "القسم", "الجنس",
                "الصفة", "اسم الأب", "العنوان", "تاريخ الدخول"
            ])
            self.transferred_students = pd.DataFrame(columns=[
                "الاسم", "اللقب", "تاريخ الميلاد", "القسم",
                "المؤسسة المستقبلة", "تاريخ الخروج"
            ])
            self.grades_data = pd.DataFrame(columns=[
                "اسم الطالب", "اللقب", "القسم", "المادة", "الدرجة", "تاريخ"
            ])

            # Load settings first to ensure defaults are set
            self.settings = self.load_settings()
            self.apply_theme_settings()

            self.attendance_data = self.load_attendance_data()

            # Create directories if they don't exist
            for d in ["reports", "data", "backups", "logs"]:
                if not os.path.exists(d):
                    os.makedirs(d)

            self.setup_login_screen()

            # Schedule automatic backup if enabled
            if self.settings.get("auto_backup", False):
                self.schedule_backup()

            LOGGERS['system'].info("Educational Management System initialized successfully")

        except Exception as e:
            LOGGERS['error'].error(f"Failed to initialize system: {e}")
            if hasattr(self, 'root'):
                messagebox.showerror("خطأ في التهيئة", f"فشل في تهيئة النظام: {e}")
            raise

    def init_user_system(self):
        """Initialize enhanced user management system"""
        try:
            self.users_file = "data/users.json"
            self.load_users()
            LOGGERS['system'].info("User system initialized")
        except Exception as e:
            LOGGERS['error'].error(f"Failed to initialize user system: {e}")
            # Create default users if file doesn't exist
            self.create_default_users()

    def create_default_users(self):
        """Create default admin and teacher users"""
        default_users = {
            "admin": {
                "password": hash_password("admin"),
                "role": "admin",
                "created_date": datetime.now().isoformat(),
                "last_login": None,
                "active": True
            },
            "teacher": {
                "password": hash_password("teacher"),
                "role": "teacher",
                "created_date": datetime.now().isoformat(),
                "last_login": None,
                "active": True
            }
        }
        self.users = default_users
        self.save_users()
        LOGGERS['system'].info("Default users created")

    def load_users(self):
        """Load users from JSON file"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    self.users = json.load(f)
                LOGGERS['data'].info("Users loaded successfully")
            else:
                self.create_default_users()
        except Exception as e:
            LOGGERS['error'].error(f"Failed to load users: {e}")
            self.create_default_users()

    def save_users(self):
        """Save users to JSON file"""
        try:
            os.makedirs(os.path.dirname(self.users_file), exist_ok=True)
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, ensure_ascii=False, indent=4)
            LOGGERS['data'].info("Users saved successfully")
        except Exception as e:
            LOGGERS['error'].error(f"Failed to save users: {e}")

    def on_closing(self):
        if messagebox.askokcancel(self.reshaper_text("خروج"), self.reshaper_text("هل أنت متأكد أنك تريد الخروج؟ سيتم حفظ البيانات تلقائيًا.")):
            try:
                # Log session end
                if self.current_username:
                    session_duration = datetime.now() - self.session_start_time if self.session_start_time else timedelta(0)
                    LOGGERS['user'].info(f"User {self.current_username} session ended. Duration: {session_duration}")

                self.save_all_data()
                LOGGERS['system'].info("System shutdown initiated")
                self.root.destroy()
            except Exception as e:
                LOGGERS['error'].error(f"Error during shutdown: {e}")
                self.root.destroy()

    def load_settings(self):
        default_settings = {
            "theme": "arc",
            "colors": {
                "primary": "#007bff",
                "secondary": "#6c757d",
                "success": "#28a745"
            },
            "auto_backup": True,
            "backup_interval": 24,
            "font_family": "Arial",
            "font_size": 10
        }
        try:
            with open("settings.json", "r", encoding="utf-8") as f:
                settings = json.load(f)
                for key, default_value in default_settings.items():
                    if key not in settings:
                        settings[key] = default_value
                for color_key, default_color in default_settings["colors"].items():
                    if color_key not in settings["colors"]:
                        settings["colors"][color_key] = default_color
                return settings
        except (FileNotFoundError, json.JSONDecodeError):
            self.save_settings(default_settings)
            return default_settings

    def save_settings(self, settings):
        with open("settings.json", "w", encoding="utf-8") as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)
        self.apply_theme_settings()

    def apply_theme_settings(self):
        current_theme = self.settings.get("theme", "arc")
        self.root.set_theme(current_theme)

        style = ttk.Style()
        primary_color = self.settings["colors"].get("primary", "#007bff")
        secondary_color = self.settings["colors"].get("secondary", "#6c757d")

        style.configure("Accent.TButton", background=primary_color,
                        foreground="white", font=(self.settings["font_family"], self.settings["font_size"] + 2, "bold"))
        style.map("Accent.TButton",
                  background=[('active', secondary_color), ('!disabled', primary_color)],
                  foreground=[('active', 'white'), ('!disabled', 'white')])

        style.configure("Dashboard.TButton",
                        font=(self.settings["font_family"], self.settings["font_size"] + 4, "bold"),
                        padding=15,
                        background=primary_color,
                        foreground="white")
        style.map("Dashboard.TButton",
                  background=[('active', secondary_color), ('!disabled', primary_color)],
                  foreground=[('active', 'white'), ('!disabled', 'white')])

        default_font_family = self.settings.get("font_family", "Arial")
        default_font_size = self.settings.get("font_size", 10)
        default_font = (default_font_family, default_font_size)

        self.root.option_add("*Font", default_font)
        style.configure("TLabel", font=default_font)
        style.configure("TButton", font=default_font)
        style.configure("TEntry", font=default_font)
        style.configure("TCombobox", font=default_font)
        style.configure("TCheckbutton", font=default_font)
        style.configure("TRadiobutton", font=default_font)
        style.configure("TSpinbox", font=default_font)
        style.configure("TLabelFrame", font=(default_font_family, default_font_size + 1, "bold"))
        style.configure("Treeview.Heading", font=(default_font_family, default_font_size + 1, "bold"))
        style.configure("Treeview", font=default_font)

    def load_attendance_data(self):
        try:
            if os.path.exists('data/attendance.json'):
                with open('data/attendance.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"daily": {}, "monthly": {}}
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء تحميل بيانات الغياب: {e}"))
            return {"daily": {}, "monthly": {}}

    def save_attendance_data(self):
        try:
            with open('data/attendance.json', 'w', encoding='utf-8') as f:
                json.dump(self.attendance_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء حفظ بيانات الغياب: {e}"))

    def load_students_data(self):
        try:
            if os.path.exists('data/students.xlsx'):
                self.students_data = pd.read_excel('data/students.xlsx')
            else:
                self.students_data = pd.DataFrame(columns=[
                    "الاسم", "اللقب", "تاريخ الميلاد", "القسم", "الجنس",
                    "الصفة", "اسم الأب", "العنوان", "تاريخ الدخول"
                ])
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء تحميل بيانات الطلاب: {e}"))
            self.students_data = pd.DataFrame(columns=[
                "الاسم", "اللقب", "تاريخ الميلاد", "القسم", "الجنس",
                "الصفة", "اسم الأب", "العنوان", "تاريخ الدخول"
            ])

    def save_students_data(self):
        try:
            self.students_data.to_excel('data/students.xlsx', index=False)
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء حفظ بيانات الطلاب: {e}"))

    def load_transferred_students_data(self):
        try:
            if os.path.exists('data/transferred_students.xlsx'):
                self.transferred_students = pd.read_excel('data/transferred_students.xlsx')
            else:
                self.transferred_students = pd.DataFrame(columns=[
                    "الاسم", "اللقب", "تاريخ الميلاد", "القسم",
                    "المؤسسة المستقبلة", "تاريخ الخروج"
                ])
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء تحميل بيانات الطلاب المحولين: {e}"))
            self.transferred_students = pd.DataFrame(columns=[
                "الاسم", "اللقب", "تاريخ الميلاد", "القسم",
                "المؤسسة المستقبلة", "تاريخ الخروج"
            ])

    def save_transferred_students_data(self):
        try:
            self.transferred_students.to_excel('data/transferred_students.xlsx', index=False)
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء حفظ بيانات الطلاب المحولين: {e}"))

    def load_grades_data(self):
        try:
            if os.path.exists('data/grades.xlsx'):
                self.grades_data = pd.read_excel('data/grades.xlsx')
            else:
                self.grades_data = pd.DataFrame(columns=[
                    "اسم الطالب", "اللقب", "القسم", "المادة", "الدرجة", "تاريخ"
                ])
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء تحميل بيانات الدرجات: {e}"))
            self.grades_data = pd.DataFrame(columns=[
                "اسم الطالب", "اللقب", "القسم", "المادة", "الدرجة", "تاريخ"
            ])

    def save_grades_data(self):
        try:
            self.grades_data.to_excel('data/grades.xlsx', index=False)
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء حفظ بيانات الدرجات: {e}"))

    def save_all_data(self):
        self.save_students_data()
        self.save_transferred_students_data()
        self.save_attendance_data()
        self.save_grades_data()

    def perform_backup(self):
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_base_name = os.path.join(backup_dir, f"backup_{timestamp}")

        try:
            self.save_all_data()
            shutil.make_archive(backup_base_name, 'zip', 'data')
            messagebox.showinfo(self.reshaper_text("النسخ الاحتياطي"), self.reshaper_text(f"تم إنشاء نسخة احتياطية بنجاح: {backup_base_name}.zip"))
        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ في النسخ الاحتياطي"), self.reshaper_text(f"فشل النسخ الاحتياطي: {e}"))
        finally:
            if self.settings.get("auto_backup", False):
                self.schedule_backup()

    def schedule_backup(self):
        interval_ms = self.settings.get("backup_interval", 24) * 3600 * 1000
        self.root.after(interval_ms, self.perform_backup)

    def setup_login_screen(self):
        self.login_frame = ttk.Frame(self.root, padding="30")
        self.login_frame.pack(expand=True)

        title_label = ttk.Label(
            self.login_frame,
            text=self.reshaper_text("نظام إدارة المؤسسة التعليمية"),
            font=(self.settings["font_family"], 24, "bold"),
            foreground=self.settings["colors"]["primary"]
        )
        title_label.pack(pady=20)

        ttk.Label(self.login_frame, text=self.reshaper_text("اسم المستخدم:"), font=(self.settings["font_family"], self.settings["font_size"] + 2)).pack(pady=5)
        self.username_entry = ttk.Entry(self.login_frame, font=(self.settings["font_family"], self.settings["font_size"] + 2))
        self.username_entry.pack(pady=5, ipadx=10, ipady=5)

        ttk.Label(self.login_frame, text=self.reshaper_text("كلمة المرور:"), font=(self.settings["font_family"], self.settings["font_size"] + 2)).pack(pady=5)
        self.password_entry = ttk.Entry(self.login_frame, show="*", font=(self.settings["font_family"], self.settings["font_size"] + 2))
        self.password_entry.pack(pady=5, ipadx=10, ipady=5)

        login_button = ttk.Button(
            self.login_frame,
            text=self.reshaper_text("تسجيل الدخول"),
            command=self.login,
            style="Accent.TButton"
        )
        login_button.pack(pady=20, ipadx=20, ipady=10)

    def login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        try:
            if not username or not password:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("الرجاء إدخال اسم المستخدم وكلمة المرور"))
                return

            if username in self.users:
                user_data = self.users[username]

                # Check if user is active
                if not user_data.get("active", True):
                    messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("هذا الحساب معطل"))
                    LOGGERS['user'].warning(f"Login attempt with disabled account: {username}")
                    return

                # Verify password
                if verify_password(password, user_data["password"]):
                    # Successful login
                    self.current_user_role = user_data["role"]
                    self.current_username = username
                    self.session_start_time = datetime.now()

                    # Update last login
                    self.users[username]["last_login"] = self.session_start_time.isoformat()
                    self.save_users()

                    # Log successful login
                    LOGGERS['user'].info(f"User {username} logged in successfully with role {self.current_user_role}")

                    self.login_frame.destroy()
                    self.load_students_data()
                    self.load_transferred_students_data()
                    self.load_grades_data()
                    self.setup_dashboard()
                else:
                    messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("كلمة المرور غير صحيحة"))
                    LOGGERS['user'].warning(f"Failed login attempt for user: {username} - incorrect password")
            else:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("اسم المستخدم غير موجود"))
                LOGGERS['user'].warning(f"Failed login attempt for non-existent user: {username}")

        except Exception as e:
            LOGGERS['error'].error(f"Login error: {e}")
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("حدث خطأ أثناء تسجيل الدخول"))

    def setup_dashboard(self):
        for widget in self.root.winfo_children():
            widget.destroy()

        self.dashboard_frame = ttk.Frame(self.root, padding="20")
        self.dashboard_frame.pack(expand=True, fill="both")

        title_label = ttk.Label(
            self.dashboard_frame,
            text=self.reshaper_text("لوحة تحكم نظام إدارة المؤسسة التعليمية"),
            font=(self.settings["font_family"], 24, "bold"),
            foreground=self.settings["colors"]["primary"]
        )
        title_label.pack(pady=20)

        buttons_frame = ttk.Frame(self.dashboard_frame)
        buttons_frame.pack(expand=True, fill="both", pady=20)

        button_configs = [
            ("إدارة الطلاب", self.manage_students, "admin"),
            ("إدارة الغياب", self.manage_attendance, "admin"),
            ("إدارة الدرجات", self.manage_grades, "admin"),
            ("الطلاب المحولون", self.manage_transferred, "admin"),
            ("تحليل البيانات", self.show_analytics, "admin"),
            ("الإعدادات", self.show_settings, "admin"),
            ("النسخ الاحتياطي واستعادة", self.show_backup_restore, "admin"),
            ("المعلومات والمساعدة", self.show_help_info, "teacher")
        ]

        row_num = 0
        col_num = 0
        for text, command, required_role in button_configs:
            if self.current_user_role == "admin" or self.current_user_role == required_role:
                button = ttk.Button(
                    buttons_frame,
                    text=self.reshaper_text(text),
                    command=command,
                    style="Dashboard.TButton"
                )
                button.grid(row=row_num, column=col_num, padx=15, pady=15, sticky="nsew")
                col_num += 1
                if col_num > 2:
                    col_num = 0
                    row_num += 1

        for i in range(3):
            buttons_frame.grid_columnconfigure(i, weight=1)
        for i in range(row_num + 1):
            buttons_frame.grid_rowconfigure(i, weight=1)

        info_frame = ttk.LabelFrame(self.dashboard_frame, text=self.reshaper_text("ملخص النظام"), padding="10")
        info_frame.pack(fill="x", pady=20)

        student_count = len(self.students_data) if not self.students_data.empty else 0
        transferred_count = len(self.transferred_students) if not self.transferred_students.empty else 0
        active_students = student_count - transferred_count

        ttk.Label(info_frame, text=self.reshaper_text(f"إجمالي عدد الطلاب: {student_count}"),
                  font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=0, column=0, padx=10, pady=5, sticky="w")
        ttk.Label(info_frame, text=self.reshaper_text(f"الطلاب النشطون: {active_students}"),
                  font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=1, column=0, padx=10, pady=5, sticky="w")
        ttk.Label(info_frame, text=self.reshaper_text(f"الطلاب المحولون: {transferred_count}"),
                  font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=2, column=0, padx=10, pady=5, sticky="w")

        current_date = datetime.now().strftime("%Y-%m-%d")
        ttk.Label(info_frame, text=self.reshaper_text(f"التاريخ الحالي: {current_date}"),
                  font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=0, column=1, padx=10, pady=5, sticky="e")
        ttk.Label(info_frame, text=self.reshaper_text(f"المستخدم الحالي: {self.current_user_role}"),
                  font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=1, column=1, padx=10, pady=5, sticky="e")

        info_frame.grid_columnconfigure(0, weight=1)
        info_frame.grid_columnconfigure(1, weight=1)

    def manage_students(self):
        if self.current_user_role != "admin":
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية للوصول إلى هذه الميزة."))
            return

        self.students_window = tk.Toplevel(self.root)
        self.students_window.title(self.reshaper_text("إدارة الطلاب"))
        self.students_window.geometry("1200x800")
        self.students_window.transient(self.root)
        self.students_window.grab_set()

        main_frame = ttk.Frame(self.students_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill="x", pady=10)

        ttk.Label(search_frame, text=self.reshaper_text("بحث باللقب/الاسم:"), font=(self.settings["font_family"], self.settings["font_size"] + 1)).pack(side="right", padx=5)
        self.search_entry = ttk.Entry(search_frame, font=(self.settings["font_family"], self.settings["font_size"] + 1))
        self.search_entry.pack(side="right", padx=5, expand=True, fill="x")
        self.search_entry.bind('<KeyRelease>', self.search_students)

        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x", pady=10)

        ttk.Button(buttons_frame, text=self.reshaper_text("استيراد من ملف إكسل"), command=self.import_from_excel).pack(side="right", padx=5)
        ttk.Button(buttons_frame, text=self.reshaper_text("تصدير إلى ملف إكسل"), command=lambda: self.export_to_excel(self.students_data, "students_export")).pack(side="right", padx=5)
        ttk.Button(buttons_frame, text=self.reshaper_text("إضافة طالب"), command=self.add_new_student).pack(side="right", padx=5)

        stats_frame = ttk.LabelFrame(main_frame, text=self.reshaper_text("إحصائيات الطلاب"), padding="10")
        stats_frame.pack(fill="x", pady=10)

        self.stats_labels = {}
        stats = ["إجمالي الطلاب", "عدد الذكور", "عدد الإناث", "عدد النصف داخلي"]
        for i, stat in enumerate(stats):
            label = ttk.Label(stats_frame, text=self.reshaper_text(f"{stat}: 0"), font=(self.settings["font_family"], self.settings["font_size"] + 1))
            label.grid(row=0, column=len(stats)-i-1, padx=10, pady=5, sticky="w")
            self.stats_labels[stat] = label
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        stats_frame.grid_columnconfigure(2, weight=1)
        stats_frame.grid_columnconfigure(3, weight=1)

        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(expand=True, fill="both", pady=10)

        columns = [
            "الاسم", "اللقب", "تاريخ الميلاد", "القسم", "الجنس",
            "الصفة", "اسم الأب", "العنوان", "تاريخ الدخول"
        ]

        self.students_tree = ttk.Treeview(tree_frame, columns=columns, show="headings")
        for col in columns:
            self.students_tree.heading(col, text=self.reshaper_text(col), anchor="center")
            self.students_tree.column(col, width=100, anchor="center")

        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.students_tree.yview)
        v_scrollbar.pack(side="left", fill="y")
        self.students_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.students_tree.xview)
        h_scrollbar.pack(side="bottom", fill="x")
        self.students_tree.configure(xscrollcommand=h_scrollbar.set)

        self.students_tree.pack(expand=True, fill="both")

        self.students_tree.bind('<Double-1>', self.edit_selected_student)
        self.students_tree.bind('<Button-3>', self.show_student_context_menu)

        self.update_students_table()
        self.update_statistics()

    def show_student_context_menu(self, event):
        item_id = self.students_tree.identify_row(event.y)
        if not item_id:
            return

        self.students_tree.selection_set(item_id)

        menu = tk.Menu(self.students_tree, tearoff=0)
        menu.add_command(label=self.reshaper_text("تعديل الطالب"), command=self.edit_selected_student)
        menu.add_command(label=self.reshaper_text("حذف الطالب"), command=self.delete_selected_student)
        menu.add_command(label=self.reshaper_text("نقل الطالب"), command=self.transfer_selected_student)
        menu.post(event.x_root, event.y_root)

    def edit_selected_student(self, event=None):
        selected_item = self.students_tree.selection()
        if not selected_item:
            messagebox.showwarning(self.reshaper_text("تنبيه"), self.reshaper_text("الرجاء اختيار طالب لتعديله."))
            return

        item_id = selected_item[0]
        item_values = self.students_tree.item(item_id, 'values')
        if not item_values:
            return

        existing_data = {
            "الاسم": item_values[0],
            "اللقب": item_values[1],
            "تاريخ الميلاد": item_values[2],
            "القسم": item_values[3],
            "الجنس": item_values[4],
            "الصفة": item_values[5],
            "اسم الأب": item_values[6],
            "العنوان": item_values[7],
            "تاريخ الدخول": item_values[8]
        }
        mask = (self.students_data["الاسم"] == existing_data["الاسم"]) & \
               (self.students_data["اللقب"] == existing_data["اللقب"])
        original_index = self.students_data[mask].index[0]

        self.add_edit_student_popup(existing_data=existing_data, original_index=original_index)

    def delete_selected_student(self):
        selected_item = self.students_tree.selection()
        if not selected_item:
            messagebox.showwarning(self.reshaper_text("تنبيه"), self.reshaper_text("الرجاء اختيار طالب لحذفه."))
            return

        if messagebox.askyesno(self.reshaper_text("تأكيد الحذف"), self.reshaper_text("هل أنت متأكد أنك تريد حذف هذا الطالب؟")):
            item_values = self.students_tree.item(selected_item[0], 'values')
            student_name_to_delete = item_values[0]
            student_lastname_to_delete = item_values[1]

            mask = (self.students_data["الاسم"] == student_name_to_delete) & \
                   (self.students_data["اللقب"] == student_lastname_to_delete)
            self.students_data = self.students_data[~mask].reset_index(drop=True)

            self.students_tree.delete(selected_item[0])
            self.update_statistics()
            self.save_students_data()
            messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم حذف الطالب بنجاح."))

    def transfer_selected_student(self):
        selected_item = self.students_tree.selection()
        if not selected_item:
            messagebox.showwarning(self.reshaper_text("تنبيه"), self.reshaper_text("الرجاء اختيار طالب لنقله."))
            return

        item_values = self.students_tree.item(selected_item[0], 'values')
        student_data = {
            "الاسم": item_values[0],
            "اللقب": item_values[1],
            "تاريخ الميلاد": item_values[2],
            "القسم": item_values[3],
            "المؤسسة المستقبلة": self.reshaper_text("تحويل داخلي"),
            "تاريخ الخروج": datetime.now().strftime("%Y-%m-%d")
        }

        transfer_window = tk.Toplevel(self.students_window)
        transfer_window.title(self.reshaper_text("نقل الطالب"))
        transfer_window.geometry("400x200")
        transfer_window.transient(self.students_window)
        transfer_window.grab_set()

        ttk.Label(transfer_window, text=self.reshaper_text("المؤسسة المستقبلة:")).pack(pady=10)
        institution_entry = ttk.Entry(transfer_window, width=40)
        institution_entry.pack(pady=5)
        institution_entry.insert(0, self.reshaper_text("تحويل داخلي"))

        def confirm_transfer():
            student_data["المؤسسة المستقبلة"] = institution_entry.get().strip()
            if not student_data["المؤسسة المستقبلة"]:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("الرجاء إدخال اسم المؤسسة المستقبلة."))
                return

            if messagebox.askyesno(self.reshaper_text("تأكيد النقل"), self.reshaper_text(f"هل أنت متأكد أنك تريد نقل الطالب {student_data['الاسم']} {student_data['اللقب']}؟")):
                mask = (self.students_data["الاسم"] == student_data["الاسم"]) & \
                       (self.students_data["اللقب"] == student_data["اللقب"]) & \
                       (self.students_data["تاريخ الميلاد"] == str(student_data["تاريخ الميلاد"]))
                self.students_data = self.students_data[~mask].reset_index(drop=True)

                self.add_to_transferred(student_data)

                self.students_tree.delete(selected_item[0])
                self.update_statistics()
                self.save_students_data()
                self.save_transferred_students_data()
                transfer_window.destroy()
                messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم نقل الطالب بنجاح إلى قائمة المغادرين."))
            else:
                transfer_window.destroy()

        ttk.Button(transfer_window, text=self.reshaper_text("تأكيد النقل"), command=confirm_transfer, style="Accent.TButton").pack(pady=10)

    def add_edit_student_popup(self, existing_data=None, original_index=None):
        add_edit_window = tk.Toplevel(self.students_window)
        add_edit_window.title(self.reshaper_text("تعديل طالب" if existing_data else "إضافة طالب جديد"))
        add_edit_window.geometry("600x700")
        add_edit_window.transient(self.students_window)
        add_edit_window.grab_set()

        form_frame = ttk.Frame(add_edit_window, padding="20")
        form_frame.pack(expand=True, fill="both")

        fields_config = [
            ("الاسم", "Entry"), ("اللقب", "Entry"), ("تاريخ الميلاد", "Entry"),
            ("القسم", "Entry"), ("الجنس", "Combobox", [self.reshaper_text("ذكر"), self.reshaper_text("أنثى")]),
            ("الصفة", "Combobox", [self.reshaper_text("داخلي"), self.reshaper_text("نصف داخلي"), self.reshaper_text("خارجي")]),
            ("اسم الأب", "Entry"), ("العنوان", "Entry")
        ]

        entries = {}
        for i, (field_name, widget_type, *options) in enumerate(fields_config):
            ttk.Label(form_frame, text=self.reshaper_text(f"{field_name}:"), font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=i, column=0, padx=5, pady=5, sticky="w")
            if widget_type == "Entry":
                entry = ttk.Entry(form_frame, font=(self.settings["font_family"], self.settings["font_size"] + 1), width=40)
            elif widget_type == "Combobox":
                entry = ttk.Combobox(form_frame, values=options[0], font=(self.settings["font_family"], self.settings["font_size"] + 1), width=38)
                if not existing_data:
                    entry.set(options[0][0])
            entry.grid(row=i, column=1, padx=5, pady=5, sticky="ew")
            entries[field_name] = entry

            if existing_data:
                val = existing_data.get(field_name, "")
                if widget_type == "Combobox":
                    entry.set(val)
                else:
                    entry.insert(0, val)

        form_frame.grid_columnconfigure(1, weight=1)

        def save_student_data():
            data = {field: entry.get() for field, entry in entries.items()}
            for key in ["الجنس", "الصفة"]:
                data[key] = get_display(arabic_reshaper.reshape(data[key]))

            if not all(data.values()):
                messagebox.showwarning(self.reshaper_text("بيانات ناقصة"), self.reshaper_text("الرجاء ملء جميع الحقول."))
                return

            if existing_data:
                for col, value in data.items():
                    self.students_data.at[original_index, col] = value
                messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم تعديل بيانات الطالب بنجاح."))
            else:
                data["تاريخ الدخول"] = datetime.now().strftime("%Y-%m-%d")
                new_student = pd.DataFrame([data])
                self.students_data = pd.concat([self.students_data, new_student], ignore_index=True)
                messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم إضافة الطالب بنجاح."))

            self.update_students_table()
            self.update_statistics()
            self.save_students_data()
            add_edit_window.destroy()

        save_button = ttk.Button(
            form_frame,
            text=self.reshaper_text("حفظ"),
            command=save_student_data,
            style="Accent.TButton"
        )
        save_button.grid(row=len(fields_config), column=0, columnspan=2, pady=20, ipadx=20, ipady=5)

    def import_from_excel(self):
        file_path = filedialog.askopenfilename(
            title=self.reshaper_text("اختر ملف الإكسل"),
            filetypes=[(self.reshaper_text("ملفات الإكسل"), "*.xlsx *.xls")]
        )

        if not file_path:
            return

        try:
            new_data = pd.read_excel(file_path)

            required_cols = [
                "الاسم", "اللقب", "تاريخ الميلاد", "القسم", "الجنس",
                "الصفة", "اسم الأب", "العنوان", "تاريخ الدخول"
            ]

            missing_cols = [col for col in required_cols if col not in new_data.columns]
            if missing_cols:
                messagebox.showwarning(
                    self.reshaper_text("أعمدة ناقصة"),
                    self.reshaper_text(f"الأعمدة التالية مفقودة في ملف الإكسل: {', '.join(missing_cols)}. سيتم تعبئتها بقيم افتراضية.")
                )
                for col in missing_cols:
                    new_data[col] = ""

            mandatory_cols = ["الاسم", "اللقب"]
            for col in mandatory_cols:
                if new_data[col].isna().any() or (new_data[col] == "").any():
                    messagebox.showerror(
                        self.reshaper_text("خطأ في البيانات"),
                        self.reshaper_text(f"العمود '{col}' يحتوي على قيم فارغة. يجب أن تكون جميع القيم في هذا العمود مملوءة.")
                    )
                    return

            valid_genders = [self.reshaper_text("ذكر"), self.reshaper_text("أنثى")]
            invalid_genders = new_data[~new_data["الجنس"].isin(valid_genders)]["الجنس"].dropna().unique()
            if invalid_genders.size > 0:
                messagebox.showerror(
                    self.reshaper_text("خطأ في البيانات"),
                    self.reshaper_text(f"قيم غير صالحة في عمود 'الجنس': {', '.join(map(str, invalid_genders))}. القيم المسموح بها: {', '.join(valid_genders)}.")
                )
                return

            valid_statuses = [self.reshaper_text("داخلي"), self.reshaper_text("نصف داخلي"), self.reshaper_text("خارجي")]
            invalid_statuses = new_data[~new_data["الصفة"].isin(valid_statuses)]["الصفة"].dropna().unique()
            if invalid_statuses.size > 0:
                messagebox.showerror(
                    self.reshaper_text("خطأ في البيانات"),
                    self.reshaper_text(f"قيم غير صالحة في عمود 'الصفة': {', '.join(map(str, invalid_statuses))}. القيم المسموح بها: {', '.join(valid_statuses)}.")
                )
                return

            text_columns = ["الاسم", "اللقب", "القسم", "الجنس", "الصفة", "اسم الأب", "العنوان"]
            for col in text_columns:
                new_data[col] = new_data[col].apply(
                    lambda x: get_display(arabic_reshaper.reshape(str(x))) if pd.notnull(x) else ""
                )

            date_columns = ["تاريخ الميلاد", "تاريخ الدخول"]
            for col in date_columns:
                try:
                    new_data[col] = pd.to_datetime(new_data[col], errors='coerce').dt.strftime("%Y-%m-%d")
                    if col == "تاريخ الدخول":
                        new_data[col].fillna(datetime.now().strftime("%Y-%m-%d"), inplace=True)
                    else:
                        new_data[col].fillna("", inplace=True)
                except Exception as e:
                    messagebox.showerror(
                        self.reshaper_text("خطأ في التاريخ"),
                        self.reshaper_text(f"خطأ في تنسيق عمود '{col}': {str(e)}. يرجى التأكد من أن التواريخ بتنسيق صالح (مثل YYYY-MM-DD).")
                    )
                    return

            key_columns = ["الاسم", "اللقب", "تاريخ الميلاد"]
            duplicates = new_data.duplicated(subset=key_columns, keep=False)
            if duplicates.any():
                duplicate_rows = new_data[duplicates][key_columns]
                messagebox.showwarning(
                    self.reshaper_text("بيانات مكررة"),
                    self.reshaper_text(f"تم العثور على سجلات مكررة بناءً على {', '.join(key_columns)}:\n{duplicate_rows.to_string(index=False)}\nسيتم الاحتفاظ بأول سجل فقط.")
                )
                new_data = new_data.drop_duplicates(subset=key_columns, keep='first')

            self.students_data = pd.concat([self.students_data, new_data], ignore_index=True)
            self.students_data = self.students_data.drop_duplicates(
                subset=key_columns, keep='last'
            ).reset_index(drop=True)

            self.update_students_table()
            self.update_statistics()
            self.save_students_data()
            messagebox.showinfo(
                self.reshaper_text("نجاح"),
                self.reshaper_text(f"تم استيراد {len(new_data)} سجل بيانات بنجاح.")
            )

        except Exception as e:
            messagebox.showerror(
                self.reshaper_text("خطأ"),
                self.reshaper_text(f"حدث خطأ أثناء استيراد الملف: {str(e)}\nتأكد من أن الملف صالح ويحتوي على الأعمدة المطلوبة.")
            )

    def export_to_excel(self, dataframe, filename_prefix="export"):
        if dataframe.empty:
            messagebox.showwarning(self.reshaper_text("لا توجد بيانات"), self.reshaper_text("لا توجد بيانات لتصديرها."))
            return

        file_path = filedialog.asksaveasfilename(
            title=self.reshaper_text("حفظ ملف الإكسل"),
            defaultextension=".xlsx",
            filetypes=[(self.reshaper_text("ملفات الإكسل"), "*.xlsx")],
            initialfile=f"{self.reshaper_text(filename_prefix)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        )

        if file_path:
            try:
                df_to_export = dataframe.applymap(lambda x: self.reshaper_text(str(x)) if isinstance(x, str) else str(x))
                df_to_export.to_excel(file_path, index=False)
                messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text(f"تم تصدير البيانات بنجاح إلى:\n{file_path}"))
            except Exception as e:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء حفظ الملف: {str(e)}"))

    def update_students_table(self, data=None):
        for item in self.students_tree.get_children():
            self.students_tree.delete(item)

        df_to_display = data if data is not None else self.students_data

        for _, row in df_to_display.iterrows():
            values = [self.reshaper_text(str(row.get(col, ""))) for col in self.students_tree["columns"]]
            self.students_tree.insert("", "end", values=values)

    def update_statistics(self, data=None):
        df = data if data is not None else self.students_data

        total = len(df)
        males = len(df[df["الجنس"] == self.reshaper_text("ذكر")])
        females = len(df[df["الجنس"] == self.reshaper_text("أنثى")])
        half_internal = len(df[df["الصفة"] == self.reshaper_text("نصف داخلي")])

        self.stats_labels["إجمالي الطلاب"].configure(text=self.reshaper_text(f"إجمالي الطلاب: {total}"))
        self.stats_labels["عدد الذكور"].configure(text=self.reshaper_text(f"عدد الذكور: {males}"))
        self.stats_labels["عدد الإناث"].configure(text=self.reshaper_text(f"عدد الإناث: {females}"))
        self.stats_labels["عدد النصف داخلي"].configure(text=self.reshaper_text(f"عدد النصف داخلي: {half_internal}"))

    def search_students(self, event=None):
        search_text = self.search_entry.get().strip()

        for item in self.students_tree.get_children():
            self.students_tree.delete(item)

        if search_text:
            mask = self.students_data.apply(
                lambda row: search_text.lower() in str(row["اللقب"]).lower() or
                            search_text.lower() in str(row["الاسم"]).lower(), axis=1
            )
            filtered_data = self.students_data[mask]
            self.update_students_table(filtered_data)
            self.update_statistics(filtered_data)
        else:
            self.update_students_table()
            self.update_statistics()

    def add_new_student(self):
        self.add_edit_student_popup(existing_data=None)

    def manage_attendance(self):
        if self.current_user_role not in ["admin", "teacher"]:
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية للوصول إلى هذه الميزة."))
            return

        self.attendance_window = tk.Toplevel(self.root)
        self.attendance_window.title(self.reshaper_text("متابعة الغيابات"))
        self.attendance_window.geometry("1200x800")
        self.attendance_window.transient(self.root)
        self.attendance_window.grab_set()

        main_frame = ttk.Frame(self.attendance_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill="x", pady=10)

        ttk.Label(search_frame, text=self.reshaper_text("البحث حسب:")).pack(side="right", padx=5)
        self.search_var = tk.StringVar()
        self.search_type = tk.StringVar(value="name")

        ttk.Radiobutton(search_frame, text=self.reshaper_text("الاسم واللقب"), variable=self.search_type, value="name",
                        command=self.search_students_attendance).pack(side="right", padx=5)
        ttk.Radiobutton(search_frame, text=self.reshaper_text("القسم"), variable=self.search_type, value="section",
                        command=self.search_students_attendance).pack(side="right", padx=5)

        self.search_entry_attendance = ttk.Entry(search_frame, textvariable=self.search_var, font=(self.settings["font_family"], self.settings["font_size"] + 1))
        self.search_entry_attendance.pack(side="right", padx=5, expand=True, fill="x")
        self.search_var.trace('w', lambda *args: self.search_students_attendance())

        stats_frame = ttk.LabelFrame(main_frame, text=self.reshaper_text("إحصائيات الغياب"), padding="10")
        stats_frame.pack(fill="x", pady=10)

        self.attendance_stats = {
            "total": ttk.Label(stats_frame, text=self.reshaper_text("إجمالي الغيابات: 0")),
            "today": ttk.Label(stats_frame, text=self.reshaper_text("غيابات اليوم: 0")),
            "month": ttk.Label(stats_frame, text=self.reshaper_text("غيابات الشهر: 0"))
        }

        for i, label in enumerate(reversed(list(self.attendance_stats.values()))):
            label.grid(row=0, column=i, padx=10, pady=5, sticky="w")
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        stats_frame.grid_columnconfigure(2, weight=1)

        columns = ["الاسم", "اللقب", "القسم"] + [self.reshaper_text(f"الساعة {i+1}") for i in range(8)] + [self.reshaper_text("تاريخ الغياب")]

        self.attendance_tree = ttk.Treeview(main_frame, columns=columns, show="headings", selectmode="browse")

        col_width = 100
        for col in columns:
            self.attendance_tree.heading(col, text=self.reshaper_text(col), anchor="center")
            self.attendance_tree.column(col, width=col_width, anchor="center")

        v_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.attendance_tree.yview)
        v_scrollbar.pack(side="left", fill="y")
        self.attendance_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(main_frame, orient="horizontal", command=self.attendance_tree.xview)
        h_scrollbar.pack(side="bottom", fill="x")
        self.attendance_tree.configure(xscrollcommand=h_scrollbar.set)

        self.attendance_tree.pack(expand=True, fill="both", pady=10)

        self.attendance_tree.bind('<Double-1>', self.toggle_absence)

        buttons_frame_bottom = ttk.Frame(main_frame)
        buttons_frame_bottom.pack(fill="x", pady=10)

        ttk.Button(buttons_frame_bottom, text=self.reshaper_text("طباعة تقرير الغيابات الشهرية"), command=self.print_monthly_attendance_by_section).pack(side="right", padx=5)
        ttk.Button(buttons_frame_bottom, text=self.reshaper_text("عرض الغيابات الشهرية"), command=self.show_monthly_attendance).pack(side="right", padx=5)
        ttk.Button(buttons_frame_bottom, text=self.reshaper_text("عرض الغيابات اليومية"), command=self.show_daily_attendance).pack(side="right", padx=5)
        ttk.Button(buttons_frame_bottom, text=self.reshaper_text("تسجيل الغياب"), command=self.record_absence, style="Accent.TButton").pack(side="right", padx=5)

        self.search_students_attendance()
        self.update_attendance_stats()

    def toggle_absence(self, event):
        region = self.attendance_tree.identify("region", event.x, event.y)
        if region == "cell":
            column_id = self.attendance_tree.identify_column(event.x)
            item_id = self.attendance_tree.identify_row(event.y)

            if not item_id or not column_id:
                return

            col_index = int(column_id.replace('#', '')) - 1

            if 3 <= col_index <= 10:
                current_value = self.attendance_tree.set(item_id, column_id)
                new_value = "" if current_value == self.reshaper_text("غائب") else self.reshaper_text("غائب")
                self.attendance_tree.set(item_id, column_id, new_value)

    def search_students_attendance(self, event=None):
        search_text = self.search_var.get().strip()
        search_type = self.search_type.get()

        for item in self.attendance_tree.get_children():
            self.attendance_tree.delete(item)

        df_to_display = self.students_data.copy()

        if search_text:
            try:
                if search_type == "name":
                    mask = (
                        df_to_display["الاسم"].str.contains(search_text, case=False, na=False) |
                        df_to_display["اللقب"].str.contains(search_text, case=False, na=False)
                    )
                else:
                    mask = df_to_display["القسم"].str.contains(search_text, case=False, na=False)

                df_to_display = df_to_display[mask]
            except Exception as e:
                messagebox.showerror(self.reshaper_text("خطأ في البحث"), self.reshaper_text(f"حدث خطأ أثناء عملية البحث: {e}"))
                return

        current_date = datetime.now().strftime("%Y-%m-%d")
        for _, row in df_to_display.iterrows():
            student_full_name = f"{row['الاسم']} {row['اللقب']}"
            absences_for_today = self.attendance_data["daily"].get(current_date, {}).get(student_full_name, {}).get("absences", [""] * 8)

            values = [
                self.reshaper_text(str(row["الاسم"])),
                self.reshaper_text(str(row["اللقب"])),
                self.reshaper_text(str(row["القسم"]))
            ] + [self.reshaper_text(a) for a in absences_for_today] + [self.reshaper_text(current_date)]

            self.attendance_tree.insert("", "end", values=values)

    def record_absence(self):
        try:
            current_date = datetime.now().strftime("%Y-%m-%d")
            current_month = datetime.now().strftime("%Y-%m")

            if current_date not in self.attendance_data["daily"]:
                self.attendance_data["daily"][current_date] = {}
            if current_month not in self.attendance_data["monthly"]:
                self.attendance_data["monthly"][current_month] = {}

            students_with_absences_recorded = 0
            for item_id in self.attendance_tree.get_children():
                values = self.attendance_tree.item(item_id)["values"]
                student_name = f"{values[0]} {values[1]}"
                section = values[2]
                absences_hours = [values[i] for i in range(3, 11)]

                absence_count = sum(1 for x in absences_hours if x == self.reshaper_text("غائب"))

                if absence_count > 0:
                    students_with_absences_recorded += 1
                    self.attendance_data["daily"][current_date][student_name] = {
                        "absences": absences_hours,
                        "count": absence_count,
                        "section": section
                    }

                    if student_name not in self.attendance_data["monthly"][current_month]:
                        self.attendance_data["monthly"][current_month][student_name] = {
                            "total": 0,
                            "section": section,
                            "days": {}
                        }

                    prev_absences_today = self.attendance_data["monthly"][current_month][student_name]["days"].get(current_date, 0)
                    self.attendance_data["monthly"][current_month][student_name]["total"] -= prev_absences_today
                    self.attendance_data["monthly"][current_month][student_name]["total"] += absence_count
                    self.attendance_data["monthly"][current_month][student_name]["days"][current_date] = absence_count

            self.save_attendance_data()
            self.update_attendance_stats()
            if students_with_absences_recorded > 0:
                messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم تسجيل الغيابات بنجاح."))
            else:
                messagebox.showinfo(self.reshaper_text("معلومة"), self.reshaper_text("لا توجد غيابات لتسجيلها لليوم."))

        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء تسجيل الغياب: {e}"))

    def update_attendance_stats(self):
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_month = datetime.now().strftime("%Y-%m")

        today_absences = sum(
            student["count"]
            for student in self.attendance_data["daily"].get(current_date, {}).values()
        )

        month_absences = sum(
            student["total"]
            for student in self.attendance_data["monthly"].get(current_month, {}).values()
        )

        total_absences = sum(
            sum(student["total"] for student in month_data.values())
            for month_data in self.attendance_data["monthly"].values()
        )

        self.attendance_stats["total"].configure(text=self.reshaper_text(f"إجمالي الغيابات: {total_absences}"))
        self.attendance_stats["today"].configure(text=self.reshaper_text(f"غيابات اليوم: {today_absences}"))
        self.attendance_stats["month"].configure(text=self.reshaper_text(f"غيابات الشهر: {month_absences}"))

    def show_daily_attendance(self):
        daily_window = tk.Toplevel(self.root)
        daily_window.title(self.reshaper_text("الغيابات اليومية"))
        daily_window.geometry("1000x600")
        daily_window.transient(self.root)
        daily_window.grab_set()

        main_frame = ttk.Frame(daily_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        date_frame = ttk.Frame(main_frame)
        date_frame.pack(fill="x", pady=10)

        ttk.Label(date_frame, text=self.reshaper_text("اختر التاريخ:")).pack(side="right", padx=5)
        date_entry = ttk.Entry(date_frame, font=(self.settings["font_family"], self.settings["font_size"] + 1))
        date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        date_entry.pack(side="right", padx=5)

        columns = ["الاسم", "القسم"] + [self.reshaper_text(f"الساعة {i+1}") for i in range(8)] + [self.reshaper_text("المجموع")]

        tree = ttk.Treeview(main_frame, columns=columns, show="headings")
        for col in columns:
            tree.heading(col, text=self.reshaper_text(col), anchor="center")
            tree.column(col, width=100, anchor="center")

        def update_daily_view():
            selected_date = date_entry.get()

            for item in tree.get_children():
                tree.delete(item)

            if selected_date in self.attendance_data["daily"]:
                for student_name, data in self.attendance_data["daily"][selected_date].items():
                    values = [
                        self.reshaper_text(student_name),
                        self.reshaper_text(data["section"])
                    ] + [self.reshaper_text(a) for a in data["absences"]] + [data["count"]]
                    tree.insert("", "end", values=values)
            else:
                messagebox.showinfo(self.reshaper_text("معلومة"), self.reshaper_text("لا توجد غيابات مسجلة لهذا التاريخ."))

        ttk.Button(date_frame, text=self.reshaper_text("عرض"), command=update_daily_view).pack(side="right", padx=5)

        tree.pack(expand=True, fill="both", pady=10)
        update_daily_view()

    def show_monthly_attendance(self):
        monthly_window = tk.Toplevel(self.root)
        monthly_window.title(self.reshaper_text("الغيابات الشهرية"))
        monthly_window.geometry("1000x600")
        monthly_window.transient(self.root)
        monthly_window.grab_set()

        main_frame = ttk.Frame(monthly_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        month_frame = ttk.Frame(main_frame)
        month_frame.pack(fill="x", pady=10)

        ttk.Label(month_frame, text=self.reshaper_text("اختر الشهر (YYYY-MM):")).pack(side="right", padx=5)
        month_entry = ttk.Entry(month_frame, font=(self.settings["font_family"], self.settings["font_size"] + 1))
        month_entry.insert(0, datetime.now().strftime("%Y-%m"))
        month_entry.pack(side="right", padx=5)

        columns = ["الاسم", "القسم", "مجموع الساعات", "مجموع الأيام", "تفاصيل الأيام"]

        tree = ttk.Treeview(main_frame, columns=columns, show="headings")
        for col in columns:
            tree.heading(col, text=self.reshaper_text(col), anchor="center")
            tree.column(col, width=150, anchor="center")

        def update_monthly_view():
            selected_month = month_entry.get()

            for item in tree.get_children():
                tree.delete(item)

            if selected_month in self.attendance_data["monthly"]:
                for student_name, data in self.attendance_data["monthly"][selected_month].items():
                    total_hours = data["total"]
                    total_days = total_hours // 7
                    remaining_hours = total_hours % 7

                    days_details = ", ".join(
                        f"{self.reshaper_text(day)}: {count}"
                        for day, count in data["days"].items()
                    )
                    values = [
                        self.reshaper_text(student_name),
                        self.reshaper_text(data["section"]),
                        total_hours,
                        f"{total_days} {self.reshaper_text('يوم')} و {remaining_hours} {self.reshaper_text('ساعة')}",
                        self.reshaper_text(days_details)
                    ]
                    tree.insert("", "end", values=values)
            else:
                messagebox.showinfo(self.reshaper_text("معلومة"), self.reshaper_text("لا توجد غيابات مسجلة لهذا الشهر."))

        ttk.Button(month_frame, text=self.reshaper_text("عرض"), command=update_monthly_view).pack(side="right", padx=5)

        tree.pack(expand=True, fill="both", pady=10)
        update_monthly_view()

    def manage_transferred(self):
        if self.current_user_role != "admin":
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية للوصول إلى هذه الميزة."))
            return

        self.transferred_window = tk.Toplevel(self.root)
        self.transferred_window.title(self.reshaper_text("الطلاب المحولون"))
        self.transferred_window.geometry("1000x600")
        self.transferred_window.transient(self.root)
        self.transferred_window.grab_set()

        main_frame = ttk.Frame(self.transferred_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        columns = [
            "الاسم", "اللقب", "تاريخ الميلاد", "القسم",
            "المؤسسة المستقبلة", "تاريخ الخروج"
        ]

        self.transferred_tree = ttk.Treeview(main_frame, columns=columns, show="headings")
        for col in columns:
            self.transferred_tree.heading(col, text=self.reshaper_text(col), anchor="center")
            self.transferred_tree.column(col, width=150, anchor="center")

        v_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.transferred_tree.yview)
        v_scrollbar.pack(side="left", fill="y")
        self.transferred_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(main_frame, orient="horizontal", command=self.transferred_tree.xview)
        h_scrollbar.pack(side="bottom", fill="x")
        self.transferred_tree.configure(xscrollcommand=h_scrollbar.set)

        self.transferred_tree.pack(expand=True, fill="both")

        self.update_transferred_students_table()

        ttk.Button(main_frame, text=self.reshaper_text("تصدير إلى إكسل"), command=lambda: self.export_to_excel(self.transferred_students, "transferred_students")).pack(pady=10)

    def update_transferred_students_table(self):
        for item in self.transferred_tree.get_children():
            self.transferred_tree.delete(item)

        if not self.transferred_students.empty:
            for _, row in self.transferred_students.iterrows():
                values = [self.reshaper_text(str(row[col])) for col in self.transferred_tree["columns"]]
                self.transferred_tree.insert("", "end", values=values)

    def add_to_transferred(self, student_data):
        try:
            new_row = pd.DataFrame([student_data])
            self.transferred_students = pd.concat([self.transferred_students, new_row], ignore_index=True)

            if hasattr(self, 'transferred_tree') and self.transferred_tree.winfo_exists():
                values = [
                    self.reshaper_text(student_data["الاسم"]),
                    self.reshaper_text(student_data["اللقب"]),
                    self.reshaper_text(student_data["تاريخ الميلاد"]),
                    self.reshaper_text(student_data["القسم"]),
                    self.reshaper_text(student_data["المؤسسة المستقبلة"]),
                    self.reshaper_text(student_data["تاريخ الخروج"])
                ]
                self.transferred_tree.insert("", "end", values=values)
            self.save_transferred_students_data()

        except Exception as e:
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء إضافة الطالب إلى قائمة المغادرين: {str(e)}"))

    def print_monthly_attendance_by_section(self):
        if self.current_user_role not in ["admin", "teacher"]:
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية لطباعة التقارير."))
            return

        try:
            if not os.path.exists("reports"):
                os.makedirs("reports")

            current_month = datetime.now().strftime("%Y-%m")
            safe_filename_base = f"monthly_attendance_report_{current_month}"
            filename = f"reports/{safe_filename_base}.pdf"

            sections_data = {}
            total_students_absent_month = 0
            total_absences_hours_month = 0

            if current_month in self.attendance_data["monthly"]:
                monthly_data = self.attendance_data["monthly"][current_month]

                for student_name, data in monthly_data.items():
                    section = data["section"]
                    if section not in sections_data:
                        sections_data[section] = {
                            "students": [],
                            "total_hours": 0,
                            "total_days": 0,
                            "student_count": 0
                        }

                    hours = data["total"]
                    days = hours // 7
                    remaining_hours = hours % 7

                    sections_data[section]["students"].append({
                        "اسم الطالب": student_name,
                        "عدد الساعات": hours,
                        "عدد الأيام": days,
                        "الساعات المتبقية": remaining_hours
                    })

                    sections_data[section]["total_hours"] += hours
                    sections_data[section]["total_days"] += days
                    sections_data[section]["student_count"] += 1
                    total_students_absent_month += 1
                    total_absences_hours_month += hours

            c = canvas.Canvas(filename, pagesize=A4)
            width, height = A4

            def write_arabic_pdf(text, x, y, font_size=12, align="right"):
                c.setFont('Arabic', font_size)
                reshaped_text = arabic_reshaper.reshape(text)
                bidi_text = get_display(reshaped_text)
                text_width = pdfmetrics.stringWidth(bidi_text, 'Arabic', font_size)
                if align == "center":
                    x = x - (text_width / 2)
                elif align == "left":
                    x = x - text_width
                c.drawString(x, y, bidi_text)

            def draw_header_pdf(page_num=None):
                write_arabic_pdf(self.reshaper_text("تقرير الغيابات الشهرية"), width / 2 + 100, height - 2 * cm, 20, align="center")
                write_arabic_pdf(self.reshaper_text(f"الشهر: {current_month}"), width - 3 * cm, height - 3.5 * cm, 14, align="right")
                write_arabic_pdf(self.reshaper_text(f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"), 2 * cm, height - 3.5 * cm, 14, align="left")
                c.line(2 * cm, height - 4 * cm, width - 2 * cm, height - 4 * cm)
                if page_num is not None:
                    write_arabic_pdf(self.reshaper_text(f"صفحة {page_num}"), width / 2, 1.5 * cm, 10, align="center")

            page_counter = 1
            draw_header_pdf(page_counter)
            y_position = height - 5 * cm

            write_arabic_pdf(self.reshaper_text("الإحصائيات العامة"), width - 3 * cm, y_position, 16, align="right")
            y_position -= 1 * cm

            stats = [
                f"إجمالي عدد الطلاب الغائبين هذا الشهر: {total_students_absent_month}",
                f"إجمالي ساعات الغياب: {total_absences_hours_month}",
                f"إجمالي أيام الغياب (افتراضي 7 ساعات/يوم): {total_absences_hours_month // 7}",
                f"متوسط الغياب للطالب الغائب: {total_absences_hours_month/total_students_absent_month:.1f} ساعة" if total_students_absent_month > 0 else "0"
            ]

            for stat in stats:
                write_arabic_pdf(self.reshaper_text(stat), width - 3 * cm, y_position, 12, align="right")
                y_position -= 0.8 * cm

            y_position -= 2 * cm
            write_arabic_pdf(self.reshaper_text("نسب الغياب حسب الأقسام (بالساعات)"), width - 3 * cm, y_position, 16, align="right")

            plt.figure(figsize=(8, 6))
            sections_labels = []
            percentages = []

            for section, data in sections_data.items():
                if data["total_hours"] > 0:
                    section_percentage = (data["total_hours"] / total_absences_hours_month * 100) if total_absences_hours_month > 0 else 0
                    sections_labels.append(get_display(arabic_reshaper.reshape(section)))
                    percentages.append(section_percentage)

            if len(sections_labels) > 0:
                plt.clf()
                plt.rcParams['font.family'] = self.settings["font_family"]
                fig, ax = plt.subplots(figsize=(8, 6))

                colors_pie = plt.cm.Set3(np.linspace(0, 1, len(sections_labels)))
                wedges, texts, autotexts = ax.pie(
                    percentages,
                    labels=sections_labels,
                    colors=colors_pie,
                    autopct='%1.1f%%',
                    startangle=90,
                    pctdistance=0.85,
                    explode=[0.05] * len(sections_labels),
                    textprops={'fontsize': 10}
                )

                plt.setp(autotexts, size=8, weight="bold")
                plt.setp(texts, size=10)

                title_text = self.reshaper_text("توزيع الغيابات حسب الأقسام")
                ax.set_title(get_display(arabic_reshaper.reshape(title_text)), pad=20, fontsize=14)

                legend_labels = [f"{sections_labels[i]} ({percentages[i]:.1f}%)" for i in range(len(sections_labels))]
                ax.legend(wedges, legend_labels, title=get_display(arabic_reshaper.reshape("الأقسام")),
                          loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))

                ax.axis('equal')

                chart_path = "reports/temp_chart.png"
                plt.savefig(chart_path, bbox_inches='tight', dpi=300, transparent=True)
                plt.close(fig)

                c.drawImage(chart_path, width / 2 - 200, y_position - 300, width=400, height=300)
                os.remove(chart_path)
            else:
                write_arabic_pdf(self.reshaper_text("لا توجد بيانات غياب لعرضها"), width / 2, y_position - 150, 14, align="center")

            c.showPage()
            page_counter += 1
            draw_header_pdf(page_counter)
            y_position = height - 5 * cm

            for section, data in sorted(sections_data.items()):
                if y_position < 10 * cm:
                    c.showPage()
                    page_counter += 1
                    draw_header_pdf(page_counter)
                    y_position = height - 5 * cm

                write_arabic_pdf(self.reshaper_text(f"القسم: {section}"), width - 3 * cm, y_position, 16, align="right")
                y_position -= 0.8 * cm

                section_stats = [
                    f"عدد الطلاب الغائبين في القسم: {data['student_count']}",
                    f"إجمالي ساعات الغياب بالقسم: {data['total_hours']}",
                    f"إجمالي أيام الغياب بالقسم: {data['total_days']}",
                    f"متوسط الغياب للطالب بالقسم: {data['total_hours']/data['student_count']:.1f} ساعة" if data['student_count'] > 0 else "0",
                    f"نسبة الغياب من المجموع الكلي: {(data['total_hours']/total_absences_hours_month*100):.1f}%" if total_absences_hours_month > 0 else "0%"
                ]

                for stat in section_stats:
                    write_arabic_pdf(self.reshaper_text(stat), width - 4 * cm, y_position, 12, align="right")
                    y_position -= 0.6 * cm

                y_position -= 0.5 * cm

                headers = ["اسم الطالب", "الساعات", "الأيام", "الساعات المتبقية"]
                col_widths = [6 * cm, 3 * cm, 3 * cm, 3 * cm]
                x_start_table = 2 * cm

                current_x_headers = x_start_table
                for i, header in enumerate(headers):
                    write_arabic_pdf(self.reshaper_text(header), current_x_headers, y_position, 12, align="left")
                    current_x_headers += col_widths[i]

                y_position -= 0.5 * cm
                c.line(2 * cm, y_position, width - 2 * cm, y_position)
                y_position -= 0.5 * cm

                students = sorted(data["students"], key=lambda x: x["عدد الساعات"], reverse=True)

                for student in students:
                    if y_position < 5 * cm:
                        c.showPage()
                        page_counter += 1
                        draw_header_pdf(page_counter)
                        y_position = height - 5 * cm
                        current_x_headers = x_start_table
                        for i, header in enumerate(headers):
                            write_arabic_pdf(self.reshaper_text(header), current_x_headers, y_position, 12, align="left")
                            current_x_headers += col_widths[i]
                        y_position -= 0.5 * cm
                        c.line(2 * cm, y_position, width - 2 * cm, y_position)
                        y_position -= 0.5 * cm

                    current_x_data = x_start_table
                    write_arabic_pdf(self.reshaper_text(student["اسم الطالب"]), current_x_data, y_position, 12, align="left")
                    current_x_data += col_widths[0]
                    write_arabic_pdf(str(student["عدد الساعات"]), current_x_data, y_position, 12, align="left")
                    current_x_data += col_widths[1]
                    write_arabic_pdf(str(student["عدد الأيام"]), current_x_data, y_position, 12, align="left")
                    current_x_data += col_widths[2]
                    write_arabic_pdf(str(student["الساعات المتبقية"]), current_x_data, y_position, 12, align="left")
                    y_position -= 0.6 * cm

                y_position -= 1.5 * cm

            c.save()
            messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text(f"تم إنشاء التقرير بنجاح في المسار:\n{filename}"))
            os.startfile(filename)

        except Exception as e:
            if os.path.exists(filename):
                os.remove(filename)
            messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء إنشاء التقرير: {str(e)}"))

    def manage_grades(self):
        if self.current_user_role not in ["admin", "teacher"]:
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية للوصول إلى هذه الميزة."))
            return

        self.grades_window = tk.Toplevel(self.root)
        self.grades_window.title(self.reshaper_text("إدارة الدرجات"))
        self.grades_window.geometry("1000x600")
        self.grades_window.transient(self.root)
        self.grades_window.grab_set()

        main_frame = ttk.Frame(self.grades_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill="x", pady=10)

        ttk.Label(search_frame, text=self.reshaper_text("البحث حسب الاسم/اللقب:")).pack(side="right", padx=5)
        self.search_grades_entry = ttk.Entry(search_frame, font=(self.settings["font_family"], self.settings["font_size"] + 1))
        self.search_grades_entry.pack(side="right", padx=5, expand=True, fill="x")
        self.search_grades_entry.bind('<KeyRelease>', self.search_grades)

        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x", pady=10)

        ttk.Button(buttons_frame, text=self.reshaper_text("إضافة درجة"), command=self.add_grade).pack(side="right", padx=5)
        ttk.Button(buttons_frame, text=self.reshaper_text("تصدير إلى إكسل"), command=lambda: self.export_to_excel(self.grades_data, "grades_export")).pack(side="right", padx=5)

        columns = ["اسم الطالب", "اللقب", "القسم", "المادة", "الدرجة", "تاريخ"]

        self.grades_tree = ttk.Treeview(main_frame, columns=columns, show="headings")
        for col in columns:
            self.grades_tree.heading(col, text=self.reshaper_text(col), anchor="center")
            self.grades_tree.column(col, width=150, anchor="center")

        v_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.grades_tree.yview)
        v_scrollbar.pack(side="left", fill="y")
        self.grades_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(main_frame, orient="horizontal", command=self.grades_tree.xview)
        h_scrollbar.pack(side="bottom", fill="x")
        self.grades_tree.configure(xscrollcommand=h_scrollbar.set)

        self.grades_tree.pack(expand=True, fill="both", pady=10)

        self.grades_tree.bind('<Double-1>', self.edit_grade)

        self.update_grades_table()

    def add_grade(self):
        add_grade_window = tk.Toplevel(self.grades_window)
        add_grade_window.title(self.reshaper_text("إضافة درجة"))
        add_grade_window.geometry("400x400")
        add_grade_window.transient(self.grades_window)
        add_grade_window.grab_set()

        form_frame = ttk.Frame(add_grade_window, padding="20")
        form_frame.pack(expand=True, fill="both")

        fields_config = [
            ("اسم الطالب", "Entry"),
            ("اللقب", "Entry"),
            ("القسم", "Entry"),
            ("المادة", "Entry"),
            ("الدرجة", "Entry"),
            ("تاريخ", "Entry")
        ]

        entries = {}
        for i, (field_name, widget_type) in enumerate(fields_config):
            ttk.Label(form_frame, text=self.reshaper_text(f"{field_name}:"), font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=i, column=0, padx=5, pady=5, sticky="w")
            entry = ttk.Entry(form_frame, font=(self.settings["font_family"], self.settings["font_size"] + 1), width=30)
            entry.grid(row=i, column=1, padx=5, pady=5, sticky="ew")
            entries[field_name] = entry
            if field_name == "تاريخ":
                entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        form_frame.grid_columnconfigure(1, weight=1)

        def save_grade():
            data = {field: entry.get().strip() for field, entry in entries.items()}
            if not all(data.values()):
                messagebox.showwarning(self.reshaper_text("بيانات ناقصة"), self.reshaper_text("الرجاء ملء جميع الحقول."))
                return

            try:
                data["الدرجة"] = float(data["الدرجة"])
                if not 0 <= data["الدرجة"] <= 20:
                    messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("الدرجة يجب أن تكون بين 0 و 20."))
                    return
            except ValueError:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("الدرجة يجب أن تكون رقمًا صالحًا."))
                return

            new_grade = pd.DataFrame([data])
            self.grades_data = pd.concat([self.grades_data, new_grade], ignore_index=True)

            self.update_grades_table()
            self.save_grades_data()
            add_grade_window.destroy()
            messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم إضافة الدرجة بنجاح."))

        ttk.Button(form_frame, text=self.reshaper_text("حفظ"), command=save_grade, style="Accent.TButton").grid(row=len(fields_config), column=0, columnspan=2, pady=20)

    def edit_grade(self, event=None):
        selected_item = self.grades_tree.selection()
        if not selected_item:
            messagebox.showwarning(self.reshaper_text("تنبيه"), self.reshaper_text("الرجاء اختيار درجة لتعديلها."))
            return

        item_id = selected_item[0]
        item_values = self.grades_tree.item(item_id, 'values')

        existing_data = {
            "اسم الطالب": item_values[0],
            "اللقب": item_values[1],
            "القسم": item_values[2],
            "المادة": item_values[3],
            "الدرجة": item_values[4],
            "تاريخ": item_values[5]
        }

        mask = (self.grades_data["اسم الطالب"] == existing_data["اسم الطالب"]) & \
               (self.grades_data["اللقب"] == existing_data["اللقب"]) & \
               (self.grades_data["المادة"] == existing_data["المادة"]) & \
               (self.grades_data["تاريخ"] == existing_data["تاريخ"])
        original_index = self.grades_data[mask].index[0]

        edit_grade_window = tk.Toplevel(self.grades_window)
        edit_grade_window.title(self.reshaper_text("تعديل درجة"))
        edit_grade_window.geometry("400x400")
        edit_grade_window.transient(self.grades_window)
        edit_grade_window.grab_set()

        form_frame = ttk.Frame(edit_grade_window, padding="20")
        form_frame.pack(expand=True, fill="both")

        fields_config = [
            ("اسم الطالب", "Entry"),
            ("اللقب", "Entry"),
            ("القسم", "Entry"),
            ("المادة", "Entry"),
            ("الدرجة", "Entry"),
            ("تاريخ", "Entry")
        ]

        entries = {}
        for i, (field_name, widget_type) in enumerate(fields_config):
            ttk.Label(form_frame, text=self.reshaper_text(f"{field_name}:"), font=(self.settings["font_family"], self.settings["font_size"] + 1)).grid(row=i, column=0, padx=5, pady=5, sticky="w")
            entry = ttk.Entry(form_frame, font=(self.settings["font_family"], self.settings["font_size"] + 1), width=30)
            entry.grid(row=i, column=1, padx=5, pady=5, sticky="ew")
            entry.insert(0, existing_data[field_name])
            entries[field_name] = entry

        form_frame.grid_columnconfigure(1, weight=1)

        def save_edited_grade():
            data = {field: entry.get().strip() for field, entry in entries.items()}
            if not all(data.values()):
                messagebox.showwarning(self.reshaper_text("بيانات ناقصة"), self.reshaper_text("الرجاء ملء جميع الحقول."))
                return

            try:
                data["الدرجة"] = float(data["الدرجة"])
                if not 0 <= data["الدرجة"] <= 20:
                    messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("الدرجة يجب أن تكون بين 0 و 20."))
                    return
            except ValueError:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text("الدرجة يجب أن تكون رقمًا صالحًا."))
                return

            for col, value in data.items():
                self.grades_data.at[original_index, col] = value

            self.update_grades_table()
            self.save_grades_data()
            edit_grade_window.destroy()
            messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم تعديل الدرجة بنجاح."))

        ttk.Button(form_frame, text=self.reshaper_text("حفظ"), command=save_edited_grade, style="Accent.TButton").grid(row=len(fields_config), column=0, columnspan=2, pady=20)

    def update_grades_table(self, data=None):
        for item in self.grades_tree.get_children():
            self.grades_tree.delete(item)

        df_to_display = data if data is not None else self.grades_data

        for _, row in df_to_display.iterrows():
            values = [self.reshaper_text(str(row.get(col, ""))) for col in self.grades_tree["columns"]]
            self.grades_tree.insert("", "end", values=values)

    def search_grades(self, event=None):
        search_text = self.search_grades_entry.get().strip()

        for item in self.grades_tree.get_children():
            self.grades_tree.delete(item)

        if search_text:
            mask = self.grades_data.apply(
                lambda row: search_text.lower() in str(row["اسم الطالب"]).lower() or
                            search_text.lower() in str(row["اللقب"]).lower(), axis=1
            )
            filtered_data = self.grades_data[mask]
            self.update_grades_table(filtered_data)
        else:
            self.update_grades_table()

    def show_analytics(self):
        if self.current_user_role != "admin":
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية للوصول إلى هذه الميزة."))
            return

        analytics_window = tk.Toplevel(self.root)
        analytics_window.title(self.reshaper_text("تحليل البيانات"))
        analytics_window.geometry("1000x600")
        analytics_window.transient(self.root)
        analytics_window.grab_set()

        main_frame = ttk.Frame(analytics_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        tabs = ttk.Notebook(main_frame)
        tabs.pack(expand=True, fill="both")

        # Students Analytics Tab
        students_tab = ttk.Frame(tabs)
        tabs.add(students_tab, text=self.reshaper_text("تحليل الطلاب"))

        students_plot_frame = ttk.Frame(students_tab)
        students_plot_frame.pack(expand=True, fill="both", pady=10)

        fig, ax = plt.subplots(figsize=(8, 4))
        gender_counts = self.students_data["الجنس"].value_counts()
        gender_counts.index = [get_display(arabic_reshaper.reshape(x)) for x in gender_counts.index]
        gender_counts.plot(kind="bar", ax=ax, color=self.settings["colors"]["primary"])
        ax.set_title(get_display(arabic_reshaper.reshape(self.reshaper_text("توزيع الطلاب حسب الجنس"))))
        ax.set_xlabel(get_display(arabic_reshaper.reshape(self.reshaper_text("الجنس"))))
        ax.set_ylabel(get_display(arabic_reshaper.reshape(self.reshaper_text("العدد"))))
        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, master=students_plot_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(expand=True, fill="both")

        # Attendance Analytics Tab
        attendance_tab = ttk.Frame(tabs)
        tabs.add(attendance_tab, text=self.reshaper_text("تحليل الغيابات"))

        attendance_plot_frame = ttk.Frame(attendance_tab)
        attendance_plot_frame.pack(expand=True, fill="both", pady=10)

        fig, ax = plt.subplots(figsize=(8, 4))
        monthly_absences = {}
        for month, data in self.attendance_data["monthly"].items():
            total = sum(student["total"] for student in data.values())
            monthly_absences[month] = total
        if monthly_absences:
            pd.Series(monthly_absences).plot(kind="line", ax=ax, marker="o", color=self.settings["colors"]["primary"])
            ax.set_title(get_display(arabic_reshaper.reshape(self.reshaper_text("الغيابات الشهرية"))))
            ax.set_xlabel(get_display(arabic_reshaper.reshape(self.reshaper_text("الشهر"))))
            ax.set_ylabel(get_display(arabic_reshaper.reshape(self.reshaper_text("عدد الساعات"))))
        else:
            ax.text(0.5, 0.5, get_display(arabic_reshaper.reshape(self.reshaper_text("لا توجد بيانات غياب"))),
                    ha="center", va="center", transform=ax.transAxes)
        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, master=attendance_plot_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(expand=True, fill="both")

        # Grades Analytics Tab
        grades_tab = ttk.Frame(tabs)
        tabs.add(grades_tab, text=self.reshaper_text("تحليل الدرجات"))

        grades_plot_frame = ttk.Frame(grades_tab)
        grades_plot_frame.pack(expand=True, fill="both", pady=10)

        fig, ax = plt.subplots(figsize=(8, 4))
        if not self.grades_data.empty:
            sns.histplot(self.grades_data["الدرجة"], bins=20, kde=True, ax=ax, color=self.settings["colors"]["primary"])
            ax.set_title(get_display(arabic_reshaper.reshape(self.reshaper_text("توزيع الدرجات"))))
            ax.set_xlabel(get_display(arabic_reshaper.reshape(self.reshaper_text("الدرجة"))))
            ax.set_ylabel(get_display(arabic_reshaper.reshape(self.reshaper_text("العدد"))))
        else:
            ax.text(0.5, 0.5, get_display(arabic_reshaper.reshape(self.reshaper_text("لا توجد بيانات درجات"))),
                    ha="center", va="center", transform=ax.transAxes)
        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, master=grades_plot_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(expand=True, fill="both")

    def show_settings(self):
        if self.current_user_role != "admin":
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية للوصول إلى هذه الميزة."))
            return

        settings_window = tk.Toplevel(self.root)
        settings_window.title(self.reshaper_text("الإعدادات"))
        settings_window.geometry("600x500")
        settings_window.transient(self.root)
        settings_window.grab_set()

        main_frame = ttk.Frame(settings_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        # Theme Selection
        ttk.Label(main_frame, text=self.reshaper_text("الثيم:")).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        theme_combobox = ttk.Combobox(main_frame, values=["arc", "radiance", "breeze", "equilux"], state="readonly")
        theme_combobox.set(self.settings.get("theme", "arc"))
        theme_combobox.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # Colors
        color_frame = ttk.LabelFrame(main_frame, text=self.reshaper_text("الألوان"), padding="10")
        color_frame.grid(row=1, column=0, columnspan=2, padx=5, pady=10, sticky="ew")

        colors = ["primary", "secondary", "success"]
        color_entries = {}
        for i, color in enumerate(colors):
            ttk.Label(color_frame, text=self.reshaper_text(f"لون {color}:")).grid(row=i, column=0, padx=5, pady=5, sticky="w")
            entry = ttk.Entry(color_frame)
            entry.insert(0, self.settings["colors"].get(color, "#000000"))
            entry.grid(row=i, column=1, padx=5, pady=5, sticky="ew")
            color_entries[color] = entry
            ttk.Button(color_frame, text=self.reshaper_text("اختيار"), command=lambda c=color: self.choose_color(color_entries[c])).grid(row=i, column=2, padx=5, pady=5)

        # Backup Settings
        backup_frame = ttk.LabelFrame(main_frame, text=self.reshaper_text("النسخ الاحتياطي"), padding="10")
        backup_frame.grid(row=2, column=0, columnspan=2, padx=5, pady=10, sticky="ew")

        auto_backup_var = tk.BooleanVar(value=self.settings.get("auto_backup", False))
        ttk.Checkbutton(backup_frame, text=self.reshaper_text("تفعيل النسخ الاحتياطي التلقائي"), variable=auto_backup_var).grid(row=0, column=0, columnspan=2, padx=5, pady=5, sticky="w")

        ttk.Label(backup_frame, text=self.reshaper_text("الفاصل الزمني (بالساعات):")).grid(row=1, column=0, padx=5, pady=5, sticky="w")
        interval_entry = ttk.Entry(backup_frame)
        interval_entry.insert(0, str(self.settings.get("backup_interval", 24)))
        interval_entry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # Font Settings
        font_frame = ttk.LabelFrame(main_frame, text=self.reshaper_text("الخط"), padding="10")
        font_frame.grid(row=3, column=0, columnspan=2, padx=5, pady=10, sticky="ew")

        ttk.Label(font_frame, text=self.reshaper_text("نوع الخط:")).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        font_combobox = ttk.Combobox(font_frame, values=["Arial", "Times New Roman", "Courier New"], state="readonly")
        font_combobox.set(self.settings.get("font_family", "Arial"))
        font_combobox.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        ttk.Label(font_frame, text=self.reshaper_text("حجم الخط:")).grid(row=1, column=0, padx=5, pady=5, sticky="w")
        font_size_entry = ttk.Entry(font_frame)
        font_size_entry.insert(0, str(self.settings.get("font_size", 10)))
        font_size_entry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        def save_settings():
            try:
                new_settings = {
                    "theme": theme_combobox.get(),
                    "colors": {
                        color: entry.get() for color, entry in color_entries.items()
                    },
                    "auto_backup": auto_backup_var.get(),
                    "backup_interval": int(interval_entry.get()),
                    "font_family": font_combobox.get(),
                    "font_size": int(font_size_entry.get())
                }
                self.settings = new_settings
                self.save_settings(new_settings)
                self.apply_theme_settings()
                settings_window.destroy()
                messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم حفظ الإعدادات بنجاح."))
            except ValueError as e:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"الرجاء إدخال قيم صالحة: {str(e)}"))

        ttk.Button(main_frame, text=self.reshaper_text("حفظ"), command=save_settings, style="Accent.TButton").grid(row=4, column=0, columnspan=2, pady=20)

    def choose_color(self, entry):
        color = colorchooser.askcolor(title=self.reshaper_text("اختر اللون"))[1]
        if color:
            entry.delete(0, tk.END)
            entry.insert(0, color)

    def show_backup_restore(self):
        if self.current_user_role != "admin":
            messagebox.showwarning(self.reshaper_text("صلاحيات"), self.reshaper_text("ليس لديك الصلاحية للوصول إلى هذه الميزة."))
            return

        backup_window = tk.Toplevel(self.root)
        backup_window.title(self.reshaper_text("النسخ الاحتياطي واستعادة البيانات"))
        backup_window.geometry("600x400")
        backup_window.transient(self.root)
        backup_window.grab_set()

        main_frame = ttk.Frame(backup_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        ttk.Button(main_frame, text=self.reshaper_text("إنشاء نسخة احتياطية"), command=self.perform_backup).pack(pady=10)
        ttk.Button(main_frame, text=self.reshaper_text("استعادة من نسخة احتياطية"), command=self.restore_backup).pack(pady=10)

        # Display available backups
        backups_frame = ttk.LabelFrame(main_frame, text=self.reshaper_text("النسخ الاحتياطية المتوفرة"), padding="10")
        backups_frame.pack(expand=True, fill="both", pady=10)

        backups_tree = ttk.Treeview(backups_frame, columns=["ملف", "تاريخ"], show="headings")
        backups_tree.heading("ملف", text=self.reshaper_text("اسم الملف"))
        backups_tree.heading("تاريخ", text=self.reshaper_text("تاريخ الإنشاء"))
        backups_tree.column("ملف", width=300)
        backups_tree.column("تاريخ", width=150)
        backups_tree.pack(expand=True, fill="both")

        scrollbar = ttk.Scrollbar(backups_frame, orient="vertical", command=backups_tree.yview)
        scrollbar.pack(side="right", fill="y")
        backups_tree.configure(yscrollcommand=scrollbar.set)

        backup_dir = "backups"
        if os.path.exists(backup_dir):
            for backup_file in os.listdir(backup_dir):
                if backup_file.endswith(".zip"):
                    file_path = os.path.join(backup_dir, backup_file)
                    creation_time = datetime.fromtimestamp(os.path.getctime(file_path)).strftime("%Y-%m-%d %H:%M:%S")
                    backups_tree.insert("", "end", values=(self.reshaper_text(backup_file), self.reshaper_text(creation_time)))

    def restore_backup(self):
        file_path = filedialog.askopenfilename(
            title=self.reshaper_text("اختر ملف النسخة الاحتياطية"),
            filetypes=[(self.reshaper_text("ملفات النسخ الاحتياطي"), "*.zip")]
        )

        if not file_path:
            return

        if messagebox.askyesno(self.reshaper_text("تأكيد الاستعادة"), self.reshaper_text("هل أنت متأكد؟ سيتم استبدال البيانات الحالية.")):
            try:
                # Backup current data before restoring
                self.perform_backup()

                # Clear current data directory
                data_dir = "data"
                if os.path.exists(data_dir):
                    shutil.rmtree(data_dir)
                os.makedirs(data_dir)

                # Extract backup
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    zip_ref.extractall(data_dir)

                # Reload data
                self.load_students_data()
                self.load_transferred_students_data()
                self.load_grades_data()
                self.attendance_data = self.load_attendance_data()

                if hasattr(self, 'students_tree'):
                    self.update_students_table()
                    self.update_statistics()
                if hasattr(self, 'transferred_tree'):
                    self.update_transferred_students_table()
                if hasattr(self, 'grades_tree'):
                    self.update_grades_table()
                if hasattr(self, 'attendance_tree'):
                    self.search_students_attendance()

                messagebox.showinfo(self.reshaper_text("نجاح"), self.reshaper_text("تم استعادة البيانات بنجاح."))
            except Exception as e:
                messagebox.showerror(self.reshaper_text("خطأ"), self.reshaper_text(f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}"))

    def show_help_info(self):
        help_window = tk.Toplevel(self.root)
        help_window.title(self.reshaper_text("المعلومات والمساعدة"))
        help_window.geometry("600x400")
        help_window.transient(self.root)
        help_window.grab_set()

        main_frame = ttk.Frame(help_window, padding="20")
        main_frame.pack(expand=True, fill="both")

        help_text = (
            self.reshaper_text("مرحبًا بكم في نظام إدارة المؤسسة التعليمية\n\n") +
            self.reshaper_text("هذا النظام يوفر الأدوات التالية:\n") +
            self.reshaper_text("- إدارة بيانات الطلاب (إضافة، تعديل، حذف، نقل).\n") +
            self.reshaper_text("- تتبع الغيابات اليومية والشهرية.\n") +
            self.reshaper_text("- إدارة درجات الطلاب.\n") +
            self.reshaper_text("- تحليل البيانات مع الرسوم البيانية.\n") +
            self.reshaper_text("- إعدادات الثيم والألوان.\n") +
            self.reshaper_text("- النسخ الاحتياطي واستعادة البيانات.\n\n") +
            self.reshaper_text("للحصول على دعم إضافي، يرجى التواصل مع فريق الدعم على: <EMAIL>")
        )

        ttk.Label(main_frame, text=help_text, font=(self.settings["font_family"], self.settings["font_size"] + 1), justify="right").pack(pady=10)

        ttk.Button(main_frame, text=self.reshaper_text("إغلاق"), command=help_window.destroy, style="Accent.TButton").pack(pady=10)

    def reshaper_text(self, text):
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)

if __name__ == "__main__":
    app = EducationalManagementSystem()
    app.root.mainloop()
