# Enhanced Educational Management System Requirements
# نظام إدارة المؤسسة التعليمية المحسن - المتطلبات

# Core GUI Framework
ttkthemes>=3.2.2

# Data Processing and Analysis
pandas>=1.5.0
numpy>=1.21.0
openpyxl>=3.0.10

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Image Processing
Pillow>=9.0.0

# Arabic Text Support
arabic-reshaper>=2.1.3
python-bidi>=0.4.2

# PDF Generation
reportlab>=3.6.0

# Additional Utilities (if needed for future enhancements)
# Database support (optional)
# sqlite3 is included with Python standard library

# Development and Testing
# pytest>=7.0.0  # Uncomment for development
# pytest-cov>=4.0.0  # Uncomment for coverage testing

# Note: tkinter is included with most Python installations
# If tkinter is not available, install python3-tk on Linux systems:
# sudo apt-get install python3-tk

# Installation Instructions:
# pip install -r requirements.txt

# For development environment, also install:
# pip install pytest pytest-cov black flake8

# Minimum Python Version: 3.8+
