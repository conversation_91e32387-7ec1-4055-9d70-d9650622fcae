#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Educational Management System Launcher
نظام إدارة المؤسسة التعليمية المحسن - المشغل

This script provides a safe way to launch the educational management system
with proper error handling and system checks.
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_required_modules():
    """Check if all required modules are available"""
    required_modules = [
        'tkinter', 'pandas', 'matplotlib', 'seaborn', 
        'arabic_reshaper', 'reportlab', 'numpy', 'ttkthemes',
        'PIL', 'openpyxl'
    ]
    
    missing_modules = []
    
    for module_name in required_modules:
        try:
            if module_name == 'PIL':
                importlib.import_module('PIL')
            else:
                importlib.import_module(module_name)
            print(f"✅ {module_name}")
        except ImportError:
            missing_modules.append(module_name)
            print(f"❌ {module_name}")
    
    if missing_modules:
        print(f"\n❌ Missing modules: {', '.join(missing_modules)}")
        print("Please install missing modules using:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_system_files():
    """Check if required system files exist"""
    required_files = ['main.py', 'settings.json']
    
    for file_name in required_files:
        if not os.path.exists(file_name):
            print(f"❌ Missing file: {file_name}")
            return False
        print(f"✅ {file_name}")
    
    return True

def create_directories():
    """Create required directories if they don't exist"""
    directories = ['data', 'reports', 'backups', 'logs']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Directory: {directory}")

def run_system_check():
    """Run comprehensive system check"""
    print("🔍 Running system checks...")
    print("=" * 50)
    
    print("\n📋 Checking Python version...")
    if not check_python_version():
        return False
    
    print("\n📦 Checking required modules...")
    if not check_required_modules():
        return False
    
    print("\n📁 Checking system files...")
    if not check_system_files():
        return False
    
    print("\n📂 Creating/checking directories...")
    create_directories()
    
    print("\n✅ All system checks passed!")
    return True

def launch_application():
    """Launch the main application"""
    try:
        print("\n🚀 Launching Educational Management System...")
        print("=" * 50)
        
        # Import and run the main application
        from main import EducationalManagementSystem
        
        app = EducationalManagementSystem()
        app.root.mainloop()
        
    except KeyboardInterrupt:
        print("\n⚠️  Application interrupted by user")
    except Exception as e:
        print(f"\n❌ Error launching application: {e}")
        print("\nPlease check the logs directory for more details.")
        return False
    
    return True

def show_help():
    """Show help information"""
    help_text = """
Enhanced Educational Management System Launcher
نظام إدارة المؤسسة التعليمية المحسن - المشغل

Usage:
    python launch.py [options]

Options:
    --check     Run system checks only (don't launch)
    --test      Run test suite
    --help      Show this help message

Default Login Credentials:
    Admin:   username: admin,   password: admin
    Teacher: username: teacher, password: teacher

For more information, see README.md
"""
    print(help_text)

def run_tests():
    """Run the test suite"""
    try:
        print("🧪 Running test suite...")
        result = subprocess.run([sys.executable, 'test_system.py'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def main():
    """Main launcher function"""
    print("🎓 Enhanced Educational Management System")
    print("نظام إدارة المؤسسة التعليمية المحسن")
    print("=" * 50)
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h']:
            show_help()
            return
        elif arg == '--check':
            success = run_system_check()
            sys.exit(0 if success else 1)
        elif arg == '--test':
            success = run_tests()
            sys.exit(0 if success else 1)
        else:
            print(f"❌ Unknown argument: {arg}")
            show_help()
            sys.exit(1)
    
    # Run system checks
    if not run_system_check():
        print("\n❌ System checks failed. Please fix the issues above.")
        sys.exit(1)
    
    # Launch application
    success = launch_application()
    
    if success:
        print("\n👋 Thank you for using the Educational Management System!")
    else:
        print("\n❌ Application exited with errors.")
        sys.exit(1)

if __name__ == "__main__":
    main()
