#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the Enhanced Educational Management System
This script tests the core functionality and improvements made to the system.
"""

import unittest
import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add the main directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the main system components
from main import hash_password, verify_password, setup_logging, LOGGERS

class TestEducationalManagementSystem(unittest.TestCase):
    """Test cases for the Educational Management System"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test directories
        for d in ["data", "reports", "backups", "logs"]:
            os.makedirs(d, exist_ok=True)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "test_password_123"
        hashed = hash_password(password)
        
        # Test that hash is different from original password
        self.assertNotEqual(password, hashed)
        
        # Test that verification works
        self.assertTrue(verify_password(password, hashed))
        
        # Test that wrong password fails
        self.assertFalse(verify_password("wrong_password", hashed))
    
    def test_logging_setup(self):
        """Test logging system setup"""
        loggers = setup_logging()
        
        # Check that all required loggers are created
        expected_loggers = ['system', 'user', 'data', 'backup', 'error']
        for logger_name in expected_loggers:
            self.assertIn(logger_name, loggers)
        
        # Test logging functionality
        loggers['system'].info("Test log message")
        
        # Check that log file is created
        log_file = Path("logs/system.log")
        self.assertTrue(log_file.exists())
    
    def test_user_data_structure(self):
        """Test user data structure"""
        from main import EducationalManagementSystem
        
        # This will test the user system initialization
        # Note: We can't fully test the GUI components without a display
        try:
            # Test default user creation logic
            default_users = {
                "admin": {
                    "password": hash_password("admin"),
                    "role": "admin",
                    "active": True
                },
                "teacher": {
                    "password": hash_password("teacher"),
                    "role": "teacher",
                    "active": True
                }
            }
            
            # Verify structure
            for username, user_data in default_users.items():
                self.assertIn("password", user_data)
                self.assertIn("role", user_data)
                self.assertIn("active", user_data)
                self.assertTrue(user_data["active"])
                
        except Exception as e:
            # GUI components might fail in headless environment
            print(f"GUI test skipped due to display requirements: {e}")
    
    def test_data_directories(self):
        """Test that required directories are created"""
        required_dirs = ["data", "reports", "backups", "logs"]
        
        for directory in required_dirs:
            self.assertTrue(os.path.exists(directory))
    
    def test_settings_structure(self):
        """Test settings file structure"""
        # Create a test settings file
        test_settings = {
            "theme": "arc",
            "colors": {
                "primary": "#007bff",
                "secondary": "#6c757d",
                "success": "#28a745"
            },
            "auto_backup": True,
            "backup_interval": 24,
            "font_family": "Arial",
            "font_size": 10
        }
        
        with open("settings.json", "w", encoding="utf-8") as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=4)
        
        # Verify file was created and can be read
        self.assertTrue(os.path.exists("settings.json"))
        
        with open("settings.json", "r", encoding="utf-8") as f:
            loaded_settings = json.load(f)
        
        self.assertEqual(loaded_settings["theme"], "arc")
        self.assertIn("colors", loaded_settings)
        self.assertEqual(loaded_settings["auto_backup"], True)

class TestSystemIntegration(unittest.TestCase):
    """Integration tests for the system"""
    
    def test_system_startup_requirements(self):
        """Test that all required modules can be imported"""
        required_modules = [
            'tkinter', 'pandas', 'matplotlib', 'seaborn', 
            'arabic_reshaper', 'reportlab', 'numpy'
        ]
        
        for module_name in required_modules:
            try:
                __import__(module_name)
            except ImportError as e:
                self.fail(f"Required module {module_name} not available: {e}")
    
    def test_file_operations(self):
        """Test file operations used by the system"""
        # Test Excel file operations
        try:
            import pandas as pd
            
            # Create test data
            test_data = pd.DataFrame({
                'Name': ['Test Student'],
                'Grade': [85]
            })
            
            # Test saving and loading
            test_file = "test_data.xlsx"
            test_data.to_excel(test_file, index=False)
            
            loaded_data = pd.read_excel(test_file)
            self.assertEqual(len(loaded_data), 1)
            self.assertEqual(loaded_data.iloc[0]['Name'], 'Test Student')
            
            # Clean up
            os.remove(test_file)
            
        except Exception as e:
            self.fail(f"Excel operations failed: {e}")

def run_tests():
    """Run all tests"""
    print("Running Enhanced Educational Management System Tests...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestEducationalManagementSystem))
    test_suite.addTest(unittest.makeSuite(TestSystemIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall result: {'PASSED' if success else 'FAILED'}")
    
    return success

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
