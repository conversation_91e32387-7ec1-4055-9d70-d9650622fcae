#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix users.json file with proper hashed passwords
"""

import json
import hashlib
import os
from datetime import datetime

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_users_file():
    """Create users.json with proper hashed passwords"""
    
    # Create data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)
    
    # Create users with hashed passwords
    users = {
        "admin": {
            "password": hash_password("admin"),
            "role": "admin",
            "created_date": datetime.now().isoformat(),
            "last_login": None,
            "active": True
        },
        "teacher": {
            "password": hash_password("teacher"),
            "role": "teacher",
            "created_date": datetime.now().isoformat(),
            "last_login": None,
            "active": True
        }
    }
    
    # Save to file
    with open("data/users.json", "w", encoding="utf-8") as f:
        json.dump(users, f, ensure_ascii=False, indent=4)
    
    print("✅ Users file created successfully!")
    print(f"Admin password hash: {users['admin']['password']}")
    print(f"Teacher password hash: {users['teacher']['password']}")

if __name__ == "__main__":
    create_users_file()
