# Enhanced Educational Management System

## نظام إدارة المؤسسة التعليمية المحسن

A comprehensive educational management system built with Python, featuring student management, attendance tracking, grades management, and advanced analytics.

## Features / الميزات

### Core Features / الميزات الأساسية
- **Student Management / إدارة الطلاب**: Add, edit, delete, and transfer students
- **Attendance Tracking / تتبع الغياب**: Daily and monthly attendance with detailed reporting
- **Grades Management / إدارة الدرجات**: Track and manage student grades
- **Data Analytics / تحليل البيانات**: Visual charts and statistics
- **Backup & Restore / النسخ الاحتياطي**: Automatic and manual backup functionality
- **Multi-language Support / دعم متعدد اللغات**: Arabic text support with proper reshaping

### Enhanced Features / الميزات المحسنة
- **Enhanced Security / أمان محسن**: Password hashing and user session management
- **Comprehensive Logging / تسجيل شامل**: Detailed system and user activity logs
- **Improved Error Handling / معالجة أخطاء محسنة**: Better error reporting and recovery
- **User Management / إدارة المستخدمين**: Advanced user roles and permissions
- **Performance Optimization / تحسين الأداء**: Better memory management and data handling

## Installation / التثبيت

### Requirements / المتطلبات
```bash
pip install -r requirements.txt
```

Required packages:
- tkinter (usually included with Python)
- pandas
- matplotlib
- seaborn
- arabic-reshaper
- python-bidi
- reportlab
- numpy
- ttkthemes
- Pillow
- openpyxl

### Setup / الإعداد
1. Clone or download the repository
2. Install required packages
3. Run the main application:
```bash
python main.py
```

## Usage / الاستخدام

### Default Login Credentials / بيانات الدخول الافتراضية
- **Admin**: username: `admin`, password: `admin`
- **Teacher**: username: `teacher`, password: `teacher`

### First Time Setup / الإعداد الأولي
1. Run the application
2. Login with default credentials
3. Change default passwords in settings
4. Configure system preferences

## System Architecture / هيكل النظام

### Directory Structure / هيكل المجلدات
```
education_system/
├── main.py                 # Main application file
├── test_system.py         # Test suite
├── README.md              # Documentation
├── requirements.txt       # Python dependencies
├── settings.json          # System settings
├── data/                  # Data storage
│   ├── students.xlsx      # Student data
│   ├── grades.xlsx        # Grades data
│   ├── attendance.json    # Attendance data
│   ├── users.json         # User accounts
│   └── transferred_students.xlsx
├── reports/               # Generated reports
├── backups/              # Backup files
└── logs/                 # System logs
    └── system.log
```

### Key Components / المكونات الرئيسية

#### 1. User Management System / نظام إدارة المستخدمين
- Secure password hashing using SHA-256
- User session tracking
- Role-based access control
- Account activation/deactivation

#### 2. Logging System / نظام التسجيل
- Comprehensive activity logging
- Separate loggers for different components:
  - System events
  - User activities
  - Data operations
  - Backup operations
  - Error tracking

#### 3. Data Management / إدارة البيانات
- Excel-based data storage for compatibility
- JSON configuration files
- Automatic data validation
- Import/Export functionality

#### 4. Security Features / ميزات الأمان
- Password hashing and verification
- Session management
- User activity logging
- Data backup and recovery

## API Reference / مرجع البرمجة

### Core Classes / الفئات الأساسية

#### EducationalManagementSystem
Main application class that handles all system operations.

**Key Methods:**
- `init_user_system()`: Initialize user management
- `login()`: Handle user authentication
- `manage_students()`: Student management interface
- `manage_attendance()`: Attendance tracking interface
- `manage_grades()`: Grades management interface
- `show_analytics()`: Data analytics dashboard

### Utility Functions / الوظائف المساعدة

#### Security Functions
```python
hash_password(password: str) -> str
verify_password(password: str, hashed: str) -> bool
```

#### Logging Functions
```python
setup_logging() -> dict
```

## Configuration / التكوين

### Settings File / ملف الإعدادات
The `settings.json` file contains system configuration:

```json
{
    "theme": "arc",
    "colors": {
        "primary": "#007bff",
        "secondary": "#6c757d",
        "success": "#28a745"
    },
    "auto_backup": true,
    "backup_interval": 24,
    "font_family": "Arial",
    "font_size": 10
}
```

### User Configuration / تكوين المستخدمين
Users are stored in `data/users.json` with the following structure:

```json
{
    "username": {
        "password": "hashed_password",
        "role": "admin|teacher",
        "created_date": "ISO_date",
        "last_login": "ISO_date",
        "active": true
    }
}
```

## Testing / الاختبار

Run the test suite to verify system functionality:

```bash
python test_system.py
```

The test suite includes:
- Password hashing and verification tests
- Logging system tests
- Data structure validation
- File operations tests
- Integration tests

## Troubleshooting / استكشاف الأخطاء

### Common Issues / المشاكل الشائعة

1. **Arabic Font Issues**: Ensure `arial.ttf` is available in the system
2. **Permission Errors**: Run with appropriate file system permissions
3. **Module Import Errors**: Install all required packages
4. **Display Issues**: Ensure proper display environment for GUI

### Log Files / ملفات السجل
Check `logs/system.log` for detailed error information and system events.

## Contributing / المساهمة

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License / الترخيص

This project is licensed under the MIT License - see the LICENSE file for details.

## Support / الدعم

For support and questions:
- Check the logs directory for error details
- Review the documentation
- Submit issues on the project repository

---

**Version**: 2.0 Enhanced
**Last Updated**: 2025
**Language Support**: Arabic/English
